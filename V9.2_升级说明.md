# BacktestV9.2 升级说明

## 🎯 升级概述

BacktestV9.py 已成功升级到 V9.2 版本，核心引入了全新的"**战场感知与评估系统**"。该系统旨在通过动态、多维度的分析，在开盘初期快速、准确地识别出市场的"真主线"。

## 🚀 核心目标

模拟顶级游资的思维，不再依赖静态数据和固定阈值，而是通过分析**资金加速度**、**攻击结构完整性**、**情绪正反馈**这三个维度，对潜在热点板块进行动态评分，从而在多个看似强大的板块中，找出真正的"主攻方向"。

## 📋 新增功能详解

### 第一步：历史数据追踪模块 (HistoryTracker)

**位置**: § 2.1 动态数据追踪模块

**核心类**: `HistoryTracker`

**主要功能**:
- **初始化**: 包含 `sector_history` 和 `stock_history` 两个字典，存储板块和个股的时间序列数据
- **数据记录**: `add_snapshot()` 方法统一记录当前时间点的板块和个股资金流数据
- **历史查询**: `_find_past_inflow()` 内部方法，根据时间查找历史数据
- **加速度计算**: `get_acceleration()` 方法计算相对于开盘、15分钟前、30分钟前的资金增长倍数

### 第二步：主线战力评估模块 (MainlineAssessor)

**核心函数**: `assess_mainline_candidate()`

**三套评分逻辑**:

#### 1. 资金加速度评估 (满分10分)
- 获取板块的加速度数据
- 基础分 = 开盘至今增长倍数 × 1.5
- 最高不超过10分

#### 2. 结构完整性评估 (满分10分)
- **龙头断层评分**: 第1名与第2名的比值 × 2.0 (最高5分)
- **梯队健康度评分**: 
  - 第2名与第3名比值在1.2-3.0之间: 3分 (健康梯队)
  - 比值 > 3.0: 5分 (第2名也形成断层)
  - 其他情况: 1分

#### 3. 情绪正反馈评估 (满分10分)
- **涨停数量得分**: 每只涨停股2分，上限6分
- **龙头涨停加分**: 如果板块资金龙头涨停，额外加4分

#### 4. 综合评分
**权重分配**: 结构分(50%) > 加速度分(30%) > 情绪分(20%)

### 第三步：主分析函数集成

**集成位置**: `_run_analysis_core()` 函数

**新增功能**:
1. 在函数开头实例化 `HistoryTracker`
2. 每次加载数据后调用 `add_snapshot()` 记录历史
3. 在板块断层分析后插入"主线战力动态评估"模块
4. 对前2-3名候选板块进行评分
5. 输出详细的评估报告

## 📊 输出示例

```
--- 主线战力动态评估 (09:32:59) ---
[候选] 军工:
  - 综合战力分: 8.10
  - 细节: 加速度(10.0), 结构(7.0), 情绪(8.0) | 资金增速(vs_open): 10.58倍
[候选] 黄金概念:
  - 综合战力分: 1.86
  - 细节: 加速度(2.9), 结构(2.0), 情绪(0.0) | 资金增速(vs_open): 1.91倍

[结论] 绝对主线确立: 【军工】
```

## 🔧 技术实现要点

### 数据结构
```python
# 历史数据格式
{'板块/个股名称': [{'time': datetime, 'inflow': float}, ...]}

# 评估结果格式
{
    'sector_name': '板块名称',
    'final_score': 综合得分,
    'acceleration_score': 加速度得分,
    'structure_score': 结构得分,
    'emotion_score': 情绪得分,
    'acceleration_data': 加速度详细数据,
    'vs_open_ratio': 相对开盘增长倍数
}
```

### 关键算法
- **加速度计算**: 当前流入 / 历史流入
- **结构评估**: 基于资金流入排名的比值分析
- **情绪量化**: 涨停股统计 + 龙头状态判断

## 🎯 系统特点

1. **📊 动态追踪**: 实时记录板块和个股的资金流变化历史
2. **🚀 加速度分析**: 计算多时间维度的资金增长倍数
3. **🏗️ 结构评估**: 分析板块内龙头断层和梯队健康度
4. **💫 情绪反馈**: 统计涨停股数量，特别关注龙头是否涨停
5. **⚖️ 智能加权**: 结构分(50%) > 加速度分(30%) > 情绪分(20%)
6. **🎯 主线识别**: 在多个强势板块中找出真正的'绝对主线'

## 🧪 测试验证

已通过完整的单元测试和集成测试:
- ✅ HistoryTracker 功能测试
- ✅ 主线评估算法测试
- ✅ 系统集成测试
- ✅ 输出格式验证

## 📝 版本信息更新

- 日志标题: `BacktestV9.2 分析日志`
- 版本描述: `V9.2 - 战场感知与评估系统（无买入功能）`
- 启动信息: `V9.2 战场感知与评估系统`

## 💡 使用建议

1. **早盘使用**: 系统特别适合在开盘初期(9:30-10:30)使用，此时资金流向最为关键
2. **多板块竞争**: 当市场出现多个热点板块时，系统能有效识别真正的主线
3. **动态调整**: 系统会根据实时数据动态调整评分，避免静态判断的局限性

## 🔄 兼容性

- 完全向后兼容V9.0和V9.1的所有功能
- 保持原有的断层分析、涨停池分析等核心功能
- 新增功能作为独立模块，不影响现有逻辑

---

**升级完成时间**: 2025-08-04  
**升级版本**: V9.0 → V9.2  
**核心特性**: 战场感知与评估系统
