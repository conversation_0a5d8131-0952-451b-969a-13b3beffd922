修改一份现有的backtestv9.py脚本，为其增加一个名为“寻找高性价比活口”的股票筛选模块。
核心目标：
在识别出市场的“绝对主线”板块后，不是去追逐已经涨幅过高的龙头股，而是要在该板块内，筛选出那些资金已经关注、但涨幅尚在合理启动区间的“高性价比”补涨目标。
基本原理：
该策略通过分析主线板块内个股的“涨跌幅”和“资金流入排名”，将股票划分为三个区域并进行筛选：
高位过热区（剔除）： 涨幅过高的股票（如 > 8.5%），风险巨大，仅作为观察哨，不作为攻击目标。
冷宫区（剔除）： 涨幅过低甚至未涨的股票（如 < 2.0%），说明资金认可度不足或时机未到，不予考虑。
黄金启动区（筛选）： 涨幅温和（如在2.0% ~ 7.0%之间），且有主力资金流入排名支持的股票。这是我们寻找的“高性价比活口”。
具体实现步骤：
在脚本顶部添加策略参数：
在# ==================== 分析配置参数 ====================区域，添加以下新的阈值配置，以便于未来调整。
Generated python
# V9.1 新增：高性价比活口筛选策略参数
LEADER_REJECT_THRESHOLD = 8.5  # 涨幅高于此值的龙头，视为观察哨，不作为买入目标
CANDIDATE_PRICE_CEILING = 7.0  # 备选目标的涨幅上限
CANDIDATE_PRICE_FLOOR = 2.0    # 备选目标的涨幅下限，必须有启动迹象
CANDIDATE_FUND_RANK_LIMIT = 30 # 只在资金流入排名前30的股票里寻找目标
Use code with caution.
Python
添加新的筛选函数：
在脚本中，例如在§ C. 板块资金流断层分析模块之后，添加以下这个新的Python函数。
Generated python
def find_potential_targets(mainline_concept, stock_fund_data):
    """
    在主线板块中筛选高性价比的攻击目标
    :param mainline_concept: 主线概念名称
    :param stock_fund_data: 含有'概念'、'排名'、'涨跌幅'列的个股资金流入DataFrame
    :return: (被放弃的龙头, 筛选出的目标列表) - 以DataFrame的Series形式返回
    """
    # 1. 筛选出属于主线概念的股票
    # 注意：这里假设 stock_fund_data 已经包含了 '概念' 列
    mainline_stocks = stock_fund_data[stock_fund_data['概念'].str.contains(mainline_concept, na=False)].copy()
    
    if mainline_stocks.empty:
        # print(f"在主线概念【{mainline_concept}】中未找到相关股票。") # 可以选择静默处理
        return None, []

    # 2. 按资金流入排名筛选（stock_fund_data应已按资金排好序，其索引即为排名-1）
    mainline_stocks = mainline_stocks[mainline_stocks['排名'] <= CANDIDATE_FUND_RANK_LIMIT]
    
    rejected_leader = None
    potential_targets = []

    # 3. 遍历主线板块内的股票，寻找目标
    for index, stock in mainline_stocks.iterrows():
        # 规则A: 识别并放弃龙头 (这里假设资金排名第一的即为龙头)
        if stock['排名'] == 1 and '涨跌幅' in stock and stock['涨跌幅'] > LEADER_REJECT_THRESHOLD:
            rejected_leader = stock
            continue # 识别为龙头后，继续寻找其他目标

        # 规则B: 筛选“黄金启动区”的活口
        if '涨跌幅' in stock and CANDIDATE_PRICE_FLOOR <= stock['涨跌幅'] <= CANDIDATE_PRICE_CEILING:
            potential_targets.append(stock)
    
    return rejected_leader, potential_targets
Use code with caution.
Python
修改核心分析函数 _run_analysis_core：
这是最关键的一步。你需要在_run_analysis_core函数内部的主循环中，找到合适的位置调用我们新增的find_potential_targets函数。
最佳位置： 在成功识别出mainline（绝对主线概念）之后，并且在df_stocks（个股资金流数据）已经加载和处理完毕之后。具体来说，是在调用analyze_sector_funding_gap并识别出gap_info之后。
前置处理（重要）：
find_potential_targets函数依赖df_stocks中包含概念列。你现有的代码是在后面的display_stocks部分才添加概念信息的。你必须提前为df_stocks添加概念列。
请在标准化df_stocks之后，立刻使用get_stock_sectors函数和stock_sector_map为df_stocks的每一行填充其对应的概念信息。同时，也要为它创建一个排名列。
调用与打印：
在上述位置，插入以下逻辑：
Generated python
# V9.1 新增：寻找高性价比活口逻辑
# 前提：mainline 已被识别，df_stocks 已加载并处理
if mainline:
    print("\n" + "--- 策略诊断：寻找高性价比活口 ---")
    
    # 关键前置步骤：为df_stocks添加'排名'和'概念'列
    if '排名' not in df_stocks.columns:
        df_stocks['排名'] = range(1, len(df_stocks) + 1)
    
    if '概念' not in df_stocks.columns and stock_sector_map:
        concepts_list = []
        for _, row in df_stocks.iterrows():
            sectors_info = get_stock_sectors(row['名称'], stock_code=row.get('代码'), stock_sector_map=stock_sector_map)
            concepts_list.append(','.join(sectors_info.get('concepts', [])))
        df_stocks['概念'] = concepts_list

    # 调用新函数进行筛选
    rejected_leader, targets = find_potential_targets(mainline, df_stocks)
    
    # 打印筛选结果
    print(f"主线战场: 【{mainline}】")
    if rejected_leader is not None:
        print(f"观察哨 (高位过热): 【{rejected_leader['股票名称']}】({rejected_leader['股票代码']}) - 涨幅: {rejected_leader['涨跌幅']:.2f}%")
    else:
        # 即使龙头没被放弃，也要显示它的状态
        leader_stock = df_stocks[df_stocks['排名'] == 1].iloc[0]
        print(f"主线龙头: 【{leader_stock['股票名称']}】({leader_stock['股票代码']}) - 涨幅: {leader_stock['涨跌幅']:.2f}%")

    if targets:
        print("高性价比活口 (黄金启动区):")
        for target in targets:
            print(f"  -> 【{target['股票名称']}】({target['股票代码']}) - 资金排名:{target['排名']}, 涨幅:{target['涨跌幅']:.2f}%")
    else:
        print("未在黄金启动区发现高性价比目标，建议观望或关注龙头分歧机会。")
